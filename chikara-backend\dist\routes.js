import actionLogRoutes from "./features/actionlog/actionlog.routes.js";
import adminRoutes from "./features/admin/admin.routes.js";
import devRoutes from "./features/dev/dev.routes.js";
import userRoutes from "./features/user/user.routes.js";
import express from "express";
const router = express.Router();
router.use("/user", userRoutes);
router.use("/admin", actionLogRoutes);
router.use("/admin", adminRoutes);
router.use("/dev", devRoutes);
export default router;
