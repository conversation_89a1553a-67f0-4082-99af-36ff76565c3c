# ORPC Route Handling Guide

This document explains how ORPC (Open RPC) route handling works in the Chikara Academy codebase, covering both backend and frontend implementation patterns.

## Overview

ORPC provides type-safe RPC communication between the backend and frontend using:

- **Backend**: ORPC server with middleware-based authentication and validation

## Backend Implementation

### 1. Core ORPC Setup (`chikara-backend/src/lib/orpc.ts`)

The core ORPC configuration provides:

#### Route Context Creation

```typescript
export async function createContext(opts: { req: { headers: IncomingHttpHeaders } }) {
    const res = await auth.api.getSession({
        headers: fromNodeHeaders(opts.req.headers),
    });

    if (!res) {
        return { session: null, user: null };
    }

    const fetchedUser = await GetUserByIdWithAssociations(Number.parseInt(res.user.id));
    return { session: res.session, user: fetchedUser! };
}
```

#### Authentication Middleware

Four levels of authentication are provided:

- **`publicAuth`**: No authentication required
- **`isLoggedInAuth`**: Requires valid user session
- **`canMakeStateChangesAuth`**: Requires authentication + user state validation (not banned, jailed, hospitalized, in battle, or on mission)
- **`adminAuth`**: Requires admin privileges

#### Error Handling

All thrown errors are automatically logged by the ORPC error interceptor:

```typescript
export const orpcLogError = (error: unknown) => {
    const isOperationalError =
        error instanceof ORPCError &&
        [
            "BAD_REQUEST",
            "UNAUTHORIZED",
            "FORBIDDEN",
            "NOT_FOUND",
            "CONFLICT",
            "UNPROCESSABLE_CONTENT",
            "TOO_MANY_REQUESTS",
        ].includes(error.code);

    if (isOperationalError) {
        logger.operational({ code: error.code, message: error.message });
    } else {
        LogErrorStack({ error: errorObj });
    }
};
```

### 2. Route Definition Pattern

Routes are defined in feature-specific files (e.g., `explore.routes.ts`):

```typescript
export const exploreRouter = {
    // Query endpoint (read-only)
    getMapByLocation: isLoggedInAuth.handler(async ({ context }) => {
        const { id, currentMapLocation } = context.user;
        const response = await ExploreController.getExploreMapByLocation(id, currentMapLocation);
        return handleResponse(response);
    }),

    // Mutation endpoint with input validation
    interactWithNode: canMakeStateChangesAuth
        .input(exploreSchema.interactWithNode)
        .handler(async ({ input, context }) => {
            const response = await ExploreController.interactWithNode(context.user.id, input.nodeId, input.isStatic);
            return handleResponse(response);
        }),
};
```

### 3. Input Validation (`explore.validation.ts`)

Zod schemas define and validate input types:

```typescript
export const InteractWithNodeSchema = z.object({
    nodeId: z.number().int().positive(),
    isStatic: z.boolean(),
});

const exploreSchema = {
    interactWithNode: InteractWithNodeSchema,
    completeNode: CompleteNodeSchema,
    // ... other schemas
};
```

### 4. Router Registration (`orpcRouter.ts`)

All feature routers are combined into a single app router:

```typescript
export const appRouter = {
    healthCheck: publicAuth.handler(() => "OK"),
    explore: exploreRouter,
    bank: bankRouter,
    job: jobRouter,
    user: userRouter,
    // ... other routers
};

export type AppRouterClient = RouterClient<typeof appRouter>;
```

### 5. Response Handling (`utils/routeHandler.ts`)

The `handleResponse` function converts controller results to ORPC responses:

```typescript
export function handleResponse<T>(result: ControllerResult<T>): T {
    if (result.error) {
        const errorType = getErrorType(result.statusCode);
        throw new ORPCError(errorType, { message: result.error });
    }
    return result.data as T;
}
```

## Key Patterns and Best Practices

### 1. Authentication Levels

- Use `publicAuth` for public endpoints
- Use `isLoggedInAuth` for read operations requiring authentication
- Use `canMakeStateChangesAuth` for write operations
- Use `adminAuth` for admin-only operations

### 2. Input Validation

- Always define Zod schemas for inputs
- Use `.input(schema)` to attach validation to routes
- Keep validation schemas in separate files

### 3. Error Handling

- Use `handleResponse()` to convert controller results
- Throw `ORPCError` for expected errors
- Let unexpected errors bubble up for logging

This pattern provides end-to-end type safety, automatic validation, consistent error handling, and seamless integration with TanStack Query for optimal caching and state management.

## Migration Checklist

The following is a checklist of route files that still need to be converted from Express endpoints to ORPC:

- [x] actionlog.routes.ts
- [ ] admin.routes.ts
- [x] auth.routes.ts
- [x] bounty.routes.ts
- [x] casino.routes.ts
- [x] chat.routes.ts
- [x] creature.routes.ts
- [x] dailyquest.routes.ts
- [x] dev.routes.ts
- [x] infirmary.routes.ts
- [x] item.routes.js
- [x] jail.routes.ts
- [x] leaderboard.routes.ts
- [x] social.routes.ts
- [x] dropchance.routes.ts
- [x] skills.routes.ts

## Migration Steps

For each feature that needs conversion:

1. Create a new `feature-name.orpc.ts` file
2. Move routes from Express to ORPC format following the patterns in this guide
3. Update the `orpcRouter.ts` file to include the new router
4. Test thoroughly before removing the old Express routes
