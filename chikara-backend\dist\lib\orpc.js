import { os, ORPCError } from "@orpc/server";
import { fromNodeHeaders } from "better-auth/node";
import * as BattleHelpers from "../features/battle/helpers/battle.helpers.js";
import { GetUserByIdWithAssociations } from "../features/user/user.helpers.js";
import { auth } from "./auth.js";
import { logger, LogErrorStack } from "../utils/log.js";
const env = process.env.NODE_ENV || "development";
const devEnvs = new Set(["development", "dev"]);
const RESTRICT_ACCESS = process.env.RESTRICT_ACCESS === "true";
const isAdmin = (user) => user.userType === "admin";
export async function createContext(opts) {
    const res = await auth.api.getSession({
        headers: fromNodeHeaders(opts.req.headers),
    });
    if (!res) {
        return {
            session: null,
            user: null,
            headers: opts.req.headers,
        };
    }
    const fetchedUser = await GetUserByIdWithAssociations(Number.parseInt(res.user.id));
    if (!fetchedUser) {
        return { session: res.session, user: null, headers: opts.req.headers };
    }
    return {
        session: res.session,
        user: fetchedUser,
        headers: opts.req.headers,
    };
}
export const orpcLogError = (error) => {
    const isOperationalError = error instanceof ORPCError &&
        [
            "BAD_REQUEST",
            "UNAUTHORIZED",
            "FORBIDDEN",
            "NOT_FOUND",
            "CONFLICT",
            "UNPROCESSABLE_CONTENT",
            "TOO_MANY_REQUESTS",
        ].includes(error.code);
    if (isOperationalError) {
        logger.operational({
            code: error.code,
            message: error.message,
            status: error.status,
            data: error.data,
        });
    }
    else {
        const errorObj = error instanceof Error ? error : new Error(String(error));
        LogErrorStack({ error: errorObj });
    }
};
export const o = os.$context();
const requireAuth = o.middleware(async ({ context, next }) => {
    if (!context?.user) {
        throw new ORPCError("Unauthorized", { status: 403 });
    }
    return next({ context });
});
const checkUserStateConstraints = async (user) => {
    if (user.banExpires) {
        throw new ORPCError("Can't do that while banned", { status: 403 });
    }
    if (user.jailedUntil) {
        throw new ORPCError("Can't do that while in jail", { status: 403 });
    }
    if (user.hospitalisedUntil) {
        throw new ORPCError("Can't do that while in hospital", { status: 403 });
    }
    if (await BattleHelpers.IsUserInBattle(user)) {
        throw new ORPCError("Can't do that while in a battle", { status: 403 });
    }
    if (user.missionEnds) {
        throw new ORPCError("Can't do that while on a mission", { status: 403 });
    }
};
const checkRestrictedAccess = (user) => {
    if (devEnvs.has(env)) {
        return;
    }
    if (RESTRICT_ACCESS && !isAdmin(user)) {
        throw new ORPCError("Access restricted", { status: 403 });
    }
};
const requireAuthAndCanMakeStateChanges = o.middleware(async ({ context, next }) => {
    if (!context?.user) {
        throw new ORPCError("Unauthorized", { status: 403 });
    }
    await checkUserStateConstraints(context.user);
    checkRestrictedAccess(context.user);
    return next({ context });
});
const requireAdmin = o.middleware(async ({ context, next }) => {
    if (!context?.user) {
        throw new ORPCError("Unauthorized", { status: 403 });
    }
    if (!isAdmin(context.user)) {
        throw new ORPCError("Admin access required", { status: 403 });
    }
    return next({ context });
});
const requireDevEnv = o.middleware(async ({ context, next }) => {
    if (!devEnvs.has(env)) {
        throw new ORPCError("Not available in current environment", { status: 404 });
    }
    if (!context?.user) {
        throw new ORPCError("Unauthorized", { status: 403 });
    }
    return next({ context });
});
export const publicAuth = o;
export const isLoggedInAuth = publicAuth.use(requireAuth);
export const canMakeStateChangesAuth = publicAuth.use(requireAuthAndCanMakeStateChanges);
export const adminAuth = publicAuth.use(requireAdmin);
export const devEnvAuth = publicAuth.use(requireDevEnv);
